---
# 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: false
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          url: {{PROD_DB_URL}}
          username: {{PROD_DB_USERNAME}}
          password: {{PROD_DB_PASSWORD}}
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 连接测试query（配置检测连接是否有效）
        connectionTestQuery: SELECT 1
        # 多久检查一次连接的活性
        keepaliveTime: 30000

--- # redis 单机配置
spring.data:
  redis:
    # 地址
    host: {{PROD_REDIS_HOST}}
    # 端口，默认为6379
    port: {{PROD_REDIS_PORT}}
    # 数据库索引
    database: {{PROD_REDIS_DATABASE}}
    # 密码
    password: {{PROD_REDIS_PASSWORD}}
    # 连接超时时间
    timeout: {{PROD_REDIS_TIMEOUT}}
    # 是否开启ssl
    ssl: false

# 日志配置
logging:
  level:
    org.ruoyi: info
    org.springframework: warn
  config: classpath:logback-plus.xml

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token有效期 设为7天 (必定过期) 单位: 秒
  timeout: 604800
  # token临时有效期 (指定时间无操作就过期) 单位: 秒
  activity-timeout: 604800
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # 是否尝试从header里读取token
  is-read-header: true
  # 是否尝试从cookie里读取token
  is-read-cookie: false
  # token前缀
  token-prefix: "Bearer"
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz

# websocket
websocket:
  enabled: true
  # 路径
  path: '/resource/websocket'
  # 设置访问源地址
  allowedOrigins: '*'
version: '3'

services:
  mysql:
    image: mysql:8.0.33
    container_name: ruoyi-ai-mysql
    env_file:
      - ./.env
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
    #ports:
    #  - "${MYSQL_PORT}:3306"
    volumes:
      - ./mysql-init:/docker-entrypoint-initdb.d
      - ./data/mysql:/var/lib/mysql
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
    restart: always
    networks:
      - ruoyi-net

  redis:
    image: redis:6.2
    container_name: ruoyi-ai-redis
    env_file:
      - ./.env
    #ports:
    #  - "${REDIS_PORT}:6379"
    volumes:
      - ./data/redis:/data
    command: redis-server --appendonly yes ${REDIS_PASSWORD:+--requirepass ${REDIS_PASSWORD}}
    restart: always
    networks:
      - ruoyi-net

  weaviate:
    image: semitechnologies/weaviate:1.30.0
    container_name: ruoyi-ai-weaviate
    env_file:
      - ./.env
    environment:  
      - QUERY_DEFAULTS_LIMIT=${WEAVIATE_QUERY_LIMIT}  
      - AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=${WEAVIATE_ANONYMOUS_ACCESS}  
      - PERSISTENCE_DATA_PATH=${WEAVIATE_DATA_PATH}  
      - DEFAULT_VECTORIZER_MODULE=${WEAVIATE_VECTORIZER_MODULE}  
      - ENABLE_MODULES=${WEAVIATE_MODULES}  
      - CLUSTER_HOSTNAME=${WEAVIATE_CLUSTER_HOSTNAME}  
    command: --host 0.0.0.0 --port 8080 --scheme http
    ports:
      - "50050:8080"
      - "50051:50051"
    volumes:
      - ./data/weaviate:/var/lib/weaviate
    restart: always
    networks:
      - ruoyi-net

  minio:
    image: minio/minio
    container_name: ruoyi-ai-minio
    ports:
      - "9000:9000"
      - "9090:9090"
    environment:
      - MINIO_ACCESS_KEY=ruoyi
      - MINIO_SECRET_KEY=ruoyi123
    volumes:
      - ./data/minio:/data
      - ./data/minio-config:/root/.minio
    command: server /data --console-address ":9090"
    restart: always
    networks:
      - ruoyi-net

  ruoyi-backend:
    image: ruoyi-ai-backend:v2.0.5
    container_name: ruoyi-ai-backend
    env_file:
      - ./.env
    ports:
      - "${BACKEND_SERVER_PORT}:${BACKEND_SERVER_PORT}"
    volumes:
      - ./data/logs:/ruoyi/server/logs
    restart: always
    depends_on:
      - mysql
      - redis
    networks:
      - ruoyi-net

  ruoyi-admin:
    image: ruoyi-ai-admin:v2.0.5
    container_name: ruoyi-ai-admin
    ports:
      #- "8082:80"
      - "${ADMIN_SERVER_PORT}:80"
    restart: always
    depends_on:
      - ruoyi-backend
    networks:
      - ruoyi-net

  ruoyi-web:
    image: ruoyi-ai-web:v2.0.5
    container_name: ruoyi-ai-web
    ports:
      #- "8081:80"
      - "${WEB_SERVER_PORT}:80"
    restart: always
    depends_on:
      - ruoyi-backend
    networks:
      - ruoyi-net

networks:
  ruoyi-net:
    driver: bridge
